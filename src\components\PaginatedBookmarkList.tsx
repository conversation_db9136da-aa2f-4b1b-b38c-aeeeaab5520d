// 分页收藏列表组件
// 替换虚拟滚动，提供稳定的分页展示

import React, { useMemo, useEffect, useState } from 'react'
import { usePagination, useKeyboardNavigation, getRecommendedPageSize } from '../hooks/usePagination'
import Pagination from './Pagination'
import BookmarkRow from './BookmarkRow'
import BookmarkCompact from './BookmarkCompact'
import TruncatedTitle from './TruncatedTitle'
import { Bookmark, Settings, Trash2 } from 'lucide-react'
import { cn } from '../lib/utils'

export type ViewMode = 'card' | 'compact' | 'row'

export interface PaginatedBookmarkListProps {
  /** 收藏列表 */
  bookmarks: any[]
  /** 视图模式 */
  viewMode: ViewMode
  /** 高亮的收藏ID */
  highlightBookmarkId?: string | null
  /** 编辑回调 */
  onEdit: (bookmark: any) => void
  /** 删除回调 */
  onDelete: (bookmark: any) => void
  /** 点击回调 */
  onClick: (bookmark: any) => void
  /** 是否启用键盘导航 */
  enableKeyboardNavigation?: boolean
  /** 自定义页面大小 */
  customPageSize?: number
  /** 组件类名 */
  className?: string
}

/**
 * 收藏项目渲染组件
 */
const BookmarkItem: React.FC<{
  bookmark: any
  viewMode: ViewMode
  isHighlighted: boolean
  onEdit: (bookmark: any) => void
  onDelete: (bookmark: any) => void
  onClick: (bookmark: any) => void
}> = React.memo(({ bookmark, viewMode, isHighlighted, onEdit, onDelete, onClick }) => {
  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation()
    onEdit(bookmark)
  }

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation()
    onDelete(bookmark)
  }

  const handleClick = () => {
    onClick(bookmark)
  }

  switch (viewMode) {
    case 'row':
      return (
        <BookmarkRow
          bookmark={bookmark}
          isHighlighted={isHighlighted}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onClick={handleClick}
        />
      )
    
    case 'compact':
      return (
        <BookmarkCompact
          bookmark={bookmark}
          isHighlighted={isHighlighted}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onClick={handleClick}
        />
      )
    
    default: // card
      return (
        <div
          onClick={handleClick}
          className={cn(
            "border rounded-lg p-4 hover:shadow-md cursor-pointer",
            isHighlighted 
              ? 'border-primary bg-primary/5 shadow-lg' 
              : 'border-border'
          )}
        >
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center space-x-2 flex-1 min-w-0">
              {bookmark.favicon && (
                <img 
                  src={bookmark.favicon} 
                  alt="" 
                  className="w-4 h-4 flex-shrink-0"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none'
                  }}
                />
              )}
              <TruncatedTitle 
                title={bookmark.title || bookmark.url}
                maxLength={50}
                className="font-medium text-foreground"
              />
            </div>
            <div className="flex items-center space-x-1 ml-2">
              <button
                onClick={handleEdit}
                className="p-1 text-muted-foreground hover:text-foreground transition-colors"
                title="编辑收藏"
              >
                <Settings className="w-4 h-4" />
              </button>
              <button
                onClick={handleDelete}
                className="p-1 text-muted-foreground hover:text-destructive transition-colors"
                title="删除收藏"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          </div>
          
          {bookmark.description && (
            <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
              {bookmark.description}
            </p>
          )}
          
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <span className="truncate flex-1">
              {bookmark.url}
            </span>
            {bookmark.category && (
              <span className="ml-2 px-2 py-1 bg-secondary rounded-full">
                {bookmark.category}
              </span>
            )}
          </div>
        </div>
      )
  }
})

BookmarkItem.displayName = 'BookmarkItem'

/**
 * 分页收藏列表组件
 */
const PaginatedBookmarkList: React.FC<PaginatedBookmarkListProps> = ({
  bookmarks,
  viewMode,
  highlightBookmarkId,
  onEdit,
  onDelete,
  onClick,
  enableKeyboardNavigation = true,
  customPageSize,
  className
}) => {
  // 计算页面大小
  const pageSize = useMemo(() => {
    return customPageSize || getRecommendedPageSize(viewMode)
  }, [customPageSize, viewMode])

  // 使用分页Hook
  const {
    currentPage,
    totalPages,
    totalItems,
    currentItems,
    goToPage,
    setPageSize: updatePageSize
  } = usePagination(bookmarks, {
    initialPageSize: pageSize,
    persistKey: 'bookmarks'
  })

  // 当视图模式变化时更新页面大小
  useEffect(() => {
    const newPageSize = customPageSize || getRecommendedPageSize(viewMode)
    if (newPageSize !== pageSize) {
      updatePageSize(newPageSize)
    }
  }, [viewMode, customPageSize, pageSize, updatePageSize])

  // 页面切换动画状态
  const [isTransitioning, setIsTransitioning] = useState(false)

  // 带动画的页面切换
  const handlePageChange = useMemo(() => {
    return (page: number) => {
      if (page === currentPage) return

      setIsTransitioning(true)

      // 延迟切换页面，创建动画效果
      setTimeout(() => {
        goToPage(page)
        setTimeout(() => {
          setIsTransitioning(false)
        }, 150)
      }, 150)
    }
  }, [currentPage, goToPage])

  // 键盘导航
  useKeyboardNavigation(handlePageChange, currentPage, totalPages, enableKeyboardNavigation)

  // 容器样式类名
  const containerClassName = useMemo(() => {
    const baseClasses = "w-full min-h-[400px]"
    
    switch (viewMode) {
      case 'row':
        return cn(baseClasses, "space-y-2")
      case 'compact':
        return cn(baseClasses, "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4")
      default: // card
        return cn(baseClasses, "grid grid-cols-1 md:grid-cols-2 gap-4")
    }
  }, [viewMode])

  // 空状态显示
  if (bookmarks.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <Bookmark className="w-12 h-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium text-foreground mb-2">暂无收藏</h3>
        <p className="text-muted-foreground">
          还没有添加任何收藏，点击右上角的"添加收藏"按钮开始收藏您喜欢的内容吧！
        </p>
      </div>
    )
  }

  return (
    <div className={cn('space-y-4', className)}>
      {/* 收藏列表容器 */}
      <div
        className={cn(
          containerClassName,
          'transition-opacity duration-300',
          isTransitioning ? 'opacity-50' : 'opacity-100'
        )}
      >
        {currentItems.map((bookmark) => (
          <BookmarkItem
            key={bookmark.id}
            bookmark={bookmark}
            viewMode={viewMode}
            isHighlighted={bookmark.id === highlightBookmarkId}
            onEdit={onEdit}
            onDelete={onDelete}
            onClick={onClick}
          />
        ))}
      </div>

      {/* 分页控件 */}
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalItems={totalItems}
        pageSize={pageSize}
        onPageChange={handlePageChange}
        showQuickJumper={totalPages > 5}
        showSizeChanger={false} // 暂时不显示页面大小选择器
        showTotal={true}
        className="border-t pt-4"
      />

      {/* 键盘导航提示 */}
      {enableKeyboardNavigation && totalPages > 1 && (
        <div className="text-xs text-muted-foreground text-center py-2 border-t">
          <span>键盘导航：</span>
          <span className="mx-2">← → 翻页</span>
          <span className="mx-2">Home/End 首末页</span>
          <span className="mx-2">Page Up/Down 快速翻页</span>
        </div>
      )}
    </div>
  )
}

export default PaginatedBookmarkList
