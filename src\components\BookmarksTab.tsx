import React, { useState, useEffect, useMemo, useCallback } from 'react'
import { Bookmark, Plus, Search, RefreshCw } from 'lucide-react'
// shadcn/ui 组件导入
import { Button } from './ui/button'
import { Input } from './ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import BookmarkEditModal from './BookmarkEditModal'
import AddBookmarkModal from './AddBookmarkModal'
import DeleteConfirmModal from './DeleteConfirmModal'
import BookmarkSortSelector from './BookmarkSortSelector'
import ViewModeSelector from './ViewModeSelector'
import PaginatedBookmarkList from './PaginatedBookmarkList'
import { useViewMode } from '../hooks/useViewMode'
import { useViewSwitchStability, useScrollPositionLock } from '../utils/layoutStability'
import { useAdvancedSearch } from '../hooks/useAdvancedSearch'
import BookmarkSortUtils from '../utils/bookmarkSortUtils'
import type { ViewMode } from './ViewModeSelector'
import type { BookmarkSortOption } from './BookmarkSortSelector'
import type { BookmarkInput } from '../types'

/**
 * 收藏管理标签页组件 - 使用shadcn/ui组件重构
 * 提供收藏内容的管理功能，包括搜索、分类筛选、多种视图模式等
 */
const BookmarksTab: React.FC = () => {
  // 收藏数据状态
  const [bookmarks, setBookmarks] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedCategory, setSelectedCategory] = useState('all')
  
  // 排序状态
  const [sortOption, setSortOption] = useState<BookmarkSortOption>(BookmarkSortUtils.getDefaultSortOption())
  
  // 使用高级搜索Hook
  const {
    query: searchQuery,
    setQuery: setSearchQuery,
    results: searchResults,
    suggestions,
    isSearching,
    searchTime,
    addFilter,
    clearFilters
  } = useAdvancedSearch(bookmarks, {
    debounceDelay: 300,
    enableSuggestions: true,
    searchConfig: {
      fuzzySearch: true,
      caseSensitive: false,
      weights: {
        title: 3,
        description: 2,
        content: 1,
        url: 1,
        tags: 2,
        category: 1
      }
    }
  })
  
  // 编辑相关状态
  const [editingBookmark, setEditingBookmark] = useState<any>(null)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editLoading, setEditLoading] = useState(false)
  
  // 添加相关状态
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  const [addLoading, setAddLoading] = useState(false)
  
  // 删除相关状态
  const [deletingBookmark, setDeletingBookmark] = useState<any>(null)
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
  const [deleteLoading, setDeleteLoading] = useState(false)
  
  // 高亮显示状态
  const [highlightBookmarkId, setHighlightBookmarkId] = useState<string | null>(null)
  
  // 视图模式管理
  const { viewMode, setViewMode, isLoading: viewModeLoading } = useViewMode()
  
  // 视图切换稳定性管理
  const { 
    containerRef: viewContainerRef, 
    displayView: displayViewMode, 
    isTransitioning: isViewTransitioning 
  } = useViewSwitchStability(viewMode, 300)
  
  // 滚动位置保持
  const { lockScrollPosition } = useScrollPositionLock()

  /**
   * 处理视图模式切换
   */
  const handleViewModeChange = useCallback((newMode: ViewMode) => {
    if (newMode !== viewMode) {
      // 保持滚动位置
      lockScrollPosition()
      // 更新视图模式
      setViewMode(newMode)
    }
  }, [viewMode, setViewMode, lockScrollPosition])

  /**
   * 检查URL参数中是否有需要高亮的收藏项ID
   */
  const checkHighlightParameter = () => {
    const urlParams = new URLSearchParams(window.location.search)
    const highlightId = urlParams.get('highlight')
    if (highlightId) {
      setHighlightBookmarkId(highlightId)
      // 5秒后取消高亮效果
      setTimeout(() => {
        setHighlightBookmarkId(null)
      }, 5000)
    }
  }

  /**
   * 加载收藏数据
   */
  const loadBookmarks = async () => {
    try {
      setLoading(true)
      
      // 向background script请求收藏数据
      const response = await chrome.runtime.sendMessage({
        type: 'GET_BOOKMARKS',
        data: {}
      })
      
      console.log('收藏数据响应:', response)
      
      if (response && response.success) {
        setBookmarks(response.data || [])
      } else {
        console.error('获取收藏数据失败:', response?.error)
        setBookmarks([])
      }
    } catch (error) {
      console.error('加载收藏数据异常:', error)
      setBookmarks([])
    } finally {
      setLoading(false)
    }
  }

  /**
   * 应用分类筛选和排序到搜索结果
   */
  const filteredBookmarks = useMemo(() => {
    let items = searchResults.map(result => result.item)
    
    // 应用分类筛选
    if (selectedCategory !== 'all') {
      items = items.filter(bookmark => bookmark.category === selectedCategory)
    }
    
    // 应用排序
    items = BookmarkSortUtils.sortBookmarks(items, sortOption)
    
    return items
  }, [searchResults, selectedCategory, sortOption])

  /**
   * 获取所有分类
   */
  const categories = Array.from(new Set(bookmarks.map(b => b.category).filter(Boolean)))



  // 组件挂载时加载数据
  useEffect(() => {
    loadBookmarks()
    checkHighlightParameter()
  }, [])

  // 更新分类筛选器
  useEffect(() => {
    clearFilters()
    if (selectedCategory !== 'all') {
      addFilter({
        field: 'category',
        operator: 'equals',
        value: selectedCategory
      })
    }
  }, [selectedCategory, addFilter, clearFilters])

  /**
   * 处理编辑按钮点击
   */
  const handleEditClick = (bookmark: any) => {
    setEditingBookmark(bookmark)
    setIsEditModalOpen(true)
  }

  /**
   * 处理编辑保存
   */
  const handleEditSave = async (updatedBookmark: any) => {
    try {
      setEditLoading(true)
      
      // 发送更新请求到background script
      const response = await chrome.runtime.sendMessage({
        type: 'UPDATE_BOOKMARK',
        data: {
          id: updatedBookmark.id,
          updates: {
            title: updatedBookmark.title,
            url: updatedBookmark.url,
            description: updatedBookmark.description,
            category: updatedBookmark.category,
            tags: updatedBookmark.tags,
            updatedAt: new Date().toISOString()
          }
        }
      })

      if (response?.success) {
        // 更新本地状态
        setBookmarks(prevBookmarks => 
          prevBookmarks.map(bookmark => 
            bookmark.id === updatedBookmark.id 
              ? { ...bookmark, ...updatedBookmark, updatedAt: new Date().toISOString() }
              : bookmark
          )
        )
        
        // 关闭编辑模态
        setIsEditModalOpen(false)
        setEditingBookmark(null)
        
        console.log('收藏更新成功')
      } else {
        console.error('更新收藏失败:', response?.error)
        throw new Error(response?.error || '更新失败')
      }
    } catch (error) {
      console.error('更新收藏异常:', error)
      // 这里可以显示错误提示
      alert('更新收藏失败，请稍后重试')
    } finally {
      setEditLoading(false)
    }
  }

  /**
   * 处理编辑取消
   */
  const handleEditCancel = () => {
    setIsEditModalOpen(false)
    setEditingBookmark(null)
  }

  /**
   * 处理添加收藏
   */
  const handleAddBookmark = () => {
    setIsAddModalOpen(true)
  }

  /**
   * 处理添加收藏保存
   */
  const handleAddSave = async (bookmarkInput: BookmarkInput) => {
    try {
      setAddLoading(true)
      
      // 发送添加请求到background script
      const response = await chrome.runtime.sendMessage({
        type: 'ADD_BOOKMARK',
        data: bookmarkInput
      })

      if (response?.success) {
        // 重新加载收藏列表
        await loadBookmarks()
        
        // 关闭添加模态
        setIsAddModalOpen(false)
        
        console.log('收藏添加成功')
      } else {
        console.error('添加收藏失败:', response?.error)
        throw new Error(response?.error || '添加失败')
      }
    } catch (error) {
      console.error('添加收藏异常:', error)
      // 这里可以显示错误提示
      alert('添加收藏失败，请稍后重试')
    } finally {
      setAddLoading(false)
    }
  }

  /**
   * 处理添加收藏取消
   */
  const handleAddCancel = () => {
    setIsAddModalOpen(false)
  }

  /**
   * 处理删除收藏点击
   */
  const handleDeleteClick = (bookmark: any) => {
    setDeletingBookmark(bookmark)
    setIsDeleteModalOpen(true)
  }

  /**
   * 处理删除确认
   */
  const handleDeleteConfirm = async (bookmarkId: string) => {
    try {
      setDeleteLoading(true)
      
      const response = await chrome.runtime.sendMessage({
        type: 'DELETE_BOOKMARK',
        data: { id: bookmarkId }
      })

      if (response?.success) {
        // 从本地状态中移除
        setBookmarks(prevBookmarks => 
          prevBookmarks.filter(b => b.id !== bookmarkId)
        )
        
        // 关闭删除模态
        setIsDeleteModalOpen(false)
        setDeletingBookmark(null)
        
        console.log('收藏删除成功')
      } else {
        console.error('删除收藏失败:', response?.error)
        throw new Error(response?.error || '删除失败')
      }
    } catch (error) {
      console.error('删除收藏异常:', error)
      // 这里可以显示错误提示
      alert('删除收藏失败，请稍后重试')
    } finally {
      setDeleteLoading(false)
    }
  }

  /**
   * 处理删除取消
   */
  const handleDeleteCancel = () => {
    setIsDeleteModalOpen(false)
    setDeletingBookmark(null)
  }

  /**
   * 处理收藏点击
   */
  const handleBookmarkClick = (bookmark: any) => {
    if (bookmark.url) {
      window.open(bookmark.url, '_blank', 'noopener,noreferrer')
    }
  }

  return (
    <Card className="m-6">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center text-2xl">
              <Bookmark className="w-6 h-6 mr-3 text-primary-600" />
              收藏管理
            </CardTitle>
            <CardDescription className="mt-1">
              管理您的收藏内容，支持搜索、分类和多种视图模式
            </CardDescription>
          </div>
          
          <div className="flex items-center space-x-3">
            {/* 刷新按钮 */}
            <Button
              onClick={loadBookmarks}
              variant="outline"
              disabled={loading}
              title="刷新收藏列表"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              刷新
            </Button>
            
            {/* 添加收藏按钮 */}
            <Button
              onClick={handleAddBookmark}
              disabled={loading}
            >
              <Plus className="w-4 h-4 mr-2" />
              添加收藏
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {/* 搜索和筛选工具栏 - 参考标签管理页面的简洁实现 */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          {/* 搜索框 */}
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              type="text"
              placeholder="搜索收藏..."
              className={`pl-10 ${isSearching ? 'bg-muted' : ''}`}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            {isSearching && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
              </div>
            )}
            
            {/* 搜索建议下拉框 */}
            {suggestions.length > 0 && searchQuery.trim() && (
              <Card className="absolute top-full left-0 right-0 mt-1 z-10 max-h-48 overflow-y-auto">
                <CardContent className="p-0">
                  {suggestions.map((suggestion, index) => (
                    <Button
                      key={index}
                      variant="ghost"
                      className="w-full justify-start text-sm border-b last:border-b-0 rounded-none"
                      onClick={() => setSearchQuery(suggestion)}
                    >
                      {suggestion}
                    </Button>
                  ))}
                </CardContent>
              </Card>
            )}
          </div>
          
          {/* 分类选择器 */}
          <div className="flex items-center space-x-2">
            <label htmlFor="category-select" className="text-sm font-medium text-gray-700 whitespace-nowrap">
              分类:
            </label>
            <select
              id="category-select"
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="all">所有分类</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>
          
          {/* 排序选择器 */}
          <div className="flex items-center space-x-2">
            <label htmlFor="sort-select" className="text-sm font-medium text-gray-700 whitespace-nowrap">
              排序:
            </label>
            <BookmarkSortSelector
              value={sortOption}
              onChange={setSortOption}
            />
          </div>
          
          {/* 视图模式选择器 */}
          <div className="view-mode-selector-container">
            {viewModeLoading ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                <span className="text-sm text-muted-foreground">加载视图选项...</span>
              </div>
            ) : (
              <ViewModeSelector
                currentMode={viewMode}
                onModeChange={handleViewModeChange}
              />
            )}
          </div>
        </div>
        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">加载收藏数据中...</p>
          </div>
        ) : filteredBookmarks.length === 0 ? (
          <div className="text-center py-12">
            <Bookmark className="w-12 h-12 mx-auto mb-4 text-muted-foreground/50" />
            <h3 className="text-lg font-medium mb-2">
              {bookmarks.length === 0 ? '暂无收藏内容' : '没有找到匹配的收藏'}
            </h3>
            <p className="text-sm text-muted-foreground">
              {bookmarks.length === 0 ? '开始收藏您感兴趣的网页和内容吧！' : '尝试调整搜索条件或分类筛选'}
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {/* 搜索结果统计 */}
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <div>
                共找到 {filteredBookmarks.length} 个收藏
                {searchQuery.trim() && (
                  <span className="ml-2">
                    (搜索耗时: {searchTime.toFixed(1)}ms)
                  </span>
                )}
              </div>
              {searchQuery.trim() && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSearchQuery('')}
                >
                  清除搜索
                </Button>
              )}
            </div>
            
            {/* 收藏列表容器 */}
            <div
              ref={viewContainerRef}
              className={`min-h-[200px] w-full transition-opacity duration-300 ${
                isViewTransitioning ? 'opacity-75' : 'opacity-100'
              }`}
            >
              {loading ? (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                    <p className="text-muted-foreground">加载收藏数据中...</p>
                  </div>
                </div>
              ) : (
                <PaginatedBookmarkList
                  bookmarks={filteredBookmarks}
                  viewMode={displayViewMode}
                  highlightBookmarkId={highlightBookmarkId}
                  onEdit={handleEditClick}
                  onDelete={handleDeleteClick}
                  onClick={handleBookmarkClick}
                  enableKeyboardNavigation={true}
                />
              )}
            </div>
          </div>
        )}
      </CardContent>

      {/* 编辑模态窗口 */}
      {editingBookmark && (
        <BookmarkEditModal
          bookmark={editingBookmark}
          isOpen={isEditModalOpen}
          onSave={handleEditSave}
          onCancel={handleEditCancel}
          loading={editLoading}
        />
      )}

      {/* 添加收藏模态窗口 */}
      <AddBookmarkModal
        isOpen={isAddModalOpen}
        onSave={handleAddSave}
        onCancel={handleAddCancel}
        loading={addLoading}
      />

      {/* 删除确认模态窗口 */}
      <DeleteConfirmModal
        isOpen={isDeleteModalOpen}
        bookmark={deletingBookmark}
        onConfirm={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
        loading={deleteLoading}
      />
    </Card>
  )
}

export default BookmarksTab