// 分页组件
// 提供完整的分页导航功能

import React, { useState, useCallback } from 'react'
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, MoreHorizontal } from 'lucide-react'
import { Button } from './ui/button'
import { Input } from './ui/input'
import { cn } from '../lib/utils'

export interface PaginationProps {
  /** 当前页码 */
  currentPage: number
  /** 总页数 */
  totalPages: number
  /** 总条目数 */
  totalItems: number
  /** 每页条目数 */
  pageSize: number
  /** 页码变化回调 */
  onPageChange: (page: number) => void
  /** 是否显示快速跳转 */
  showQuickJumper?: boolean
  /** 是否显示页面大小选择器 */
  showSizeChanger?: boolean
  /** 页面大小变化回调 */
  onPageSizeChange?: (size: number) => void
  /** 可选的页面大小选项 */
  pageSizeOptions?: number[]
  /** 是否显示总数信息 */
  showTotal?: boolean
  /** 自定义总数显示 */
  showTotalRender?: (total: number, range: [number, number]) => React.ReactNode
  /** 组件类名 */
  className?: string
}

/**
 * 分页组件
 */
const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  totalItems,
  pageSize,
  onPageChange,
  showQuickJumper = true,
  showSizeChanger = false,
  onPageSizeChange,
  pageSizeOptions = [8, 12, 20, 50],
  showTotal = true,
  showTotalRender,
  className
}) => {
  const [jumpValue, setJumpValue] = useState('')

  // 计算当前页的数据范围
  const startIndex = (currentPage - 1) * pageSize + 1
  const endIndex = Math.min(currentPage * pageSize, totalItems)

  // 处理快速跳转
  const handleQuickJump = useCallback(() => {
    const page = parseInt(jumpValue, 10)
    if (page >= 1 && page <= totalPages) {
      onPageChange(page)
      setJumpValue('')
    }
  }, [jumpValue, totalPages, onPageChange])

  // 处理跳转输入框回车
  const handleJumpKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      handleQuickJump()
    }
  }, [handleQuickJump])

  // 生成页码按钮
  const renderPageNumbers = () => {
    const pages: React.ReactNode[] = []
    const maxVisiblePages = 7 // 最多显示7个页码按钮

    if (totalPages <= maxVisiblePages) {
      // 总页数较少，显示所有页码
      for (let i = 1; i <= totalPages; i++) {
        pages.push(
          <Button
            key={i}
            variant={i === currentPage ? 'default' : 'outline'}
            size="sm"
            onClick={() => onPageChange(i)}
            className="min-w-[32px] h-8"
          >
            {i}
          </Button>
        )
      }
    } else {
      // 总页数较多，智能显示页码
      const startPage = Math.max(1, currentPage - 2)
      const endPage = Math.min(totalPages, currentPage + 2)

      // 第一页
      if (startPage > 1) {
        pages.push(
          <Button
            key={1}
            variant={1 === currentPage ? 'default' : 'outline'}
            size="sm"
            onClick={() => onPageChange(1)}
            className="min-w-[32px] h-8"
          >
            1
          </Button>
        )
        
        if (startPage > 2) {
          pages.push(
            <div key="start-ellipsis" className="flex items-center px-2">
              <MoreHorizontal className="w-4 h-4 text-muted-foreground" />
            </div>
          )
        }
      }

      // 中间页码
      for (let i = startPage; i <= endPage; i++) {
        pages.push(
          <Button
            key={i}
            variant={i === currentPage ? 'default' : 'outline'}
            size="sm"
            onClick={() => onPageChange(i)}
            className="min-w-[32px] h-8"
          >
            {i}
          </Button>
        )
      }

      // 最后一页
      if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
          pages.push(
            <div key="end-ellipsis" className="flex items-center px-2">
              <MoreHorizontal className="w-4 h-4 text-muted-foreground" />
            </div>
          )
        }
        
        pages.push(
          <Button
            key={totalPages}
            variant={totalPages === currentPage ? 'default' : 'outline'}
            size="sm"
            onClick={() => onPageChange(totalPages)}
            className="min-w-[32px] h-8"
          >
            {totalPages}
          </Button>
        )
      }
    }

    return pages
  }

  // 如果没有数据，不显示分页
  if (totalItems === 0 || totalPages <= 1) {
    return null
  }

  return (
    <div className={cn('flex items-center justify-between gap-4 py-4', className)}>
      {/* 左侧：总数信息 */}
      <div className="flex items-center gap-4 text-sm text-muted-foreground">
        {showTotal && (
          <div>
            {showTotalRender ? (
              showTotalRender(totalItems, [startIndex, endIndex])
            ) : (
              `共 ${totalItems} 条，第 ${startIndex}-${endIndex} 条`
            )}
          </div>
        )}
        
        {showSizeChanger && onPageSizeChange && (
          <div className="flex items-center gap-2">
            <span>每页</span>
            <select
              value={pageSize}
              onChange={(e) => onPageSizeChange(Number(e.target.value))}
              className="border rounded px-2 py-1 text-sm"
            >
              {pageSizeOptions.map(size => (
                <option key={size} value={size}>{size}</option>
              ))}
            </select>
            <span>条</span>
          </div>
        )}
      </div>

      {/* 右侧：分页控件 */}
      <div className="flex items-center gap-2">
        {/* 导航按钮 */}
        <div className="flex items-center gap-1">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(1)}
            disabled={currentPage === 1}
            className="h-8 w-8 p-0"
            title="第一页 (Home)"
          >
            <ChevronsLeft className="w-4 h-4" />
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="h-8 w-8 p-0"
            title="上一页 (←)"
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>
        </div>

        {/* 页码按钮 */}
        <div className="flex items-center gap-1">
          {renderPageNumbers()}
        </div>

        {/* 导航按钮 */}
        <div className="flex items-center gap-1">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="h-8 w-8 p-0"
            title="下一页 (→)"
          >
            <ChevronRight className="w-4 h-4" />
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(totalPages)}
            disabled={currentPage === totalPages}
            className="h-8 w-8 p-0"
            title="最后一页 (End)"
          >
            <ChevronsRight className="w-4 h-4" />
          </Button>
        </div>

        {/* 快速跳转 */}
        {showQuickJumper && totalPages > 5 && (
          <div className="flex items-center gap-2 ml-4">
            <span className="text-sm text-muted-foreground">跳转到</span>
            <Input
              type="number"
              min={1}
              max={totalPages}
              value={jumpValue}
              onChange={(e) => setJumpValue(e.target.value)}
              onKeyDown={handleJumpKeyDown}
              className="w-16 h-8 text-center"
              placeholder={currentPage.toString()}
            />
            <span className="text-sm text-muted-foreground">页</span>
            <Button
              size="sm"
              onClick={handleQuickJump}
              disabled={!jumpValue || parseInt(jumpValue, 10) < 1 || parseInt(jumpValue, 10) > totalPages}
              className="h-8"
            >
              跳转
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}

export default Pagination
